'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  FileSpreadsheet,
  Trash2,
  Download,
  BarChart,
  Calendar,
  FileIcon,
  Search,
  Loader2,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  getStoredFiles,
  deleteStoredFile,
  StoredFile,
} from '@/lib/data-analysis/file-storage';
import { toast } from 'sonner';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { useDebounce } from '@/hooks/useDebounce';

dayjs.extend(relativeTime);

interface FileHistoryProps {
  onSelectFile: (file: StoredFile) => void;
}

const FileHistory: React.FC<FileHistoryProps> = ({ onSelectFile }) => {
  const [files, setFiles] = useState<StoredFile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [isDeleting, setIsDeleting] = useState<Record<string, boolean>>({});
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  useEffect(() => {
    fetchFiles();
  }, []);

  const fetchFiles = async () => {
    try {
      setIsLoading(true);
      const storedFiles = await getStoredFiles();
      setFiles(storedFiles);
    } catch (error) {
      console.error('Error fetching files:', error);
      toast.error('Failed to load file history');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteFile = useCallback(async (fileId: string) => {
    try {
      setIsDeleting((prev) => ({ ...prev, [fileId]: true }));
      await deleteStoredFile(fileId);
      setFiles(files.filter((file) => file.id !== fileId));
      toast.success('File deleted successfully');
    } catch (error) {
      console.error('Error deleting file:', error);
      toast.error('Failed to delete file');
    } finally {
      setIsDeleting((prev) => ({ ...prev, [fileId]: false }));
    }
  }, [files]);

  const handleSelectFile = useCallback((file: StoredFile) => {
    onSelectFile(file);
  }, [onSelectFile]);

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  }, []);

  // Filter files based on search term
  const filteredFiles = files.filter((file) =>
    file.name.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
  );

  // Get file icon based on file type
  const getFileIcon = (fileType: string) => {
    switch (fileType) {
      case 'csv':
        return <FileSpreadsheet className="h-8 w-8 text-green-500" />;
      case 'xlsx':
      case 'xls':
        return <FileSpreadsheet className="h-8 w-8 text-blue-500" />;
      default:
        return <FileIcon className="h-8 w-8 text-gray-500" />;
    }
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">File History</h2>
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search files..."
            className="pl-8"
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : filteredFiles.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredFiles.map((file) => (
            <div
              key={file.id}
              className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-700 hover:border-primary/50 dark:hover:border-primary/50 transition-colors"
            >
              <div className="flex items-start gap-3">
                {getFileIcon(file.type)}
                <div className="flex-1 min-w-0">
                  <h3
                    className="font-medium text-sm truncate"
                    title={file.name}
                  >
                    {file.name}
                  </h3>
                  <div className="flex items-center gap-2 text-xs text-gray-500 mt-1">
                    <span className="uppercase bg-gray-100 dark:bg-gray-700 px-1.5 py-0.5 rounded text-gray-600 dark:text-gray-300">
                      {file.type}
                    </span>
                    <span>{formatFileSize(file.size)}</span>
                  </div>
                </div>
              </div>

              <div className="mt-3 text-xs text-gray-500 space-y-1">
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  <span>Uploaded {dayjs(file.uploadDate).fromNow()}</span>
                </div>
                {file.metadata && (
                  <div className="flex items-center gap-1">
                    <BarChart className="h-3 w-3" />
                    <span>
                      {file.metadata.rowCount} rows, {file.metadata.columnCount}{' '}
                      columns
                    </span>
                  </div>
                )}
              </div>

              <div className="mt-4 flex justify-end gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 px-2"
                  onClick={() => handleDeleteFile(file.id)}
                  disabled={isDeleting[file.id]}
                >
                  {isDeleting[file.id] ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Trash2 className="h-4 w-4 text-red-500" />
                  )}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 px-2"
                  onClick={() => handleSelectFile(file)}
                >
                  <BarChart className="h-4 w-4 mr-1" />
                  Analyze
                </Button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12 bg-gray-50 dark:bg-gray-800 rounded-lg border border-dashed border-gray-300 dark:border-gray-700">
          <FileSpreadsheet className="h-12 w-12 mx-auto text-gray-400" />
          <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-gray-100">
            No files found
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {debouncedSearchTerm
              ? `No files matching "${debouncedSearchTerm}"`
              : 'Upload a file to get started with data analysis'}
          </p>
        </div>
      )}
    </div>
  );
};

export default FileHistory;
