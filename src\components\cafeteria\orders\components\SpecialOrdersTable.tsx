import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Search, Eye, Plus } from 'lucide-react';
import { GetSpecialOrders, GetStaffSpecialOrders } from '@/api/cafeteria/menu';
import { useUrlFilters } from '@/hooks/useUrlFilters';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import DateRangeFilter from '@/components/common/date-range-filter';
import Pagination from '@/components/common/pagination';
import OrderDetailsModal from './OrderDetailsModal';
import dayjs from 'dayjs';
import { currencyFormat } from '@/lib/utils';

interface SpecialOrdersTableProps {
  onNewOrder: () => void;
}

export default function SpecialOrdersTable({
  onNewOrder,
}: SpecialOrdersTableProps) {
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [orderDetailsOpen, setOrderDetailsOpen] = useState(false);

  const canManageOrders = hasPermission(PERMISSIONS.CAFETERIA_ORDERS_MANAGE);

  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    getFilter,
    getDateFilter,
    setFilter,
    setFilters,
    buildApiQuery,
  } = useUrlFilters({ initialPageSize: 15 });

  // Get current filter values from URL (no useState, no useEffect!)
  const startDate = getDateFilter('startDate');
  const endDate = getDateFilter('endDate');
  const viewType = (getFilter('viewType') as 'my' | 'all') || (canManageOrders ? 'all' : 'my');

  // Conditionally use different APIs based on viewType and permissions
  const {
    specialOrders: mySpecialOrders,
    specialLoading: mySpecialLoading,
    mutate: mutateMyOrders,
  } = GetStaffSpecialOrders(
    `?${buildApiQuery()}`
  );
  const {
    specialOrders: allSpecialOrders,
    specialLoading: allSpecialLoading,
    mutate: mutateAllOrders,
  } = canManageOrders
    ? GetSpecialOrders(`?${buildApiQuery()}`)
    : { specialOrders: null, specialLoading: false, mutate: () => {} };

  // Use appropriate data based on viewType
  const specialOrders = viewType === 'all' ? allSpecialOrders : mySpecialOrders;
  const specialLoading =
    viewType === 'all' ? allSpecialLoading : mySpecialLoading;
  const mutate = viewType === 'all' ? mutateAllOrders : mutateMyOrders;
  const orderData = specialOrders?.data?.specialOrders || [];
  const totalPages = specialOrders?.data?.totalPages ?? 0;

  // Stable event handlers
  const handleViewOrderDetails = (order: any) => {
    setSelectedOrder(order);
    setOrderDetailsOpen(true);
  };

  const handleViewTypeChange = (value: 'my' | 'all') => {
    setFilter('viewType', value);
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap-reverse items-center justify-between">
        <h2 className="text-xl font-bold">Special Orders</h2>
        <Button onClick={onNewOrder}>
          <Plus className="w-4 h-4 mr-2" />
          New Special Order
        </Button>
      </div>

      <div className="flex flex-wrap gap-3 items-center">
        <DateRangeFilter
          initialStartDate={startDate}
          initialEndDate={endDate}
          onFilterChange={(start, end) => {
            setFilters({
              startDate: start ? new Date(start) : null,
              endDate: end ? new Date(end) : null,
            });
          }}
          className="w-[250px]"
        />
        <div className="relative w-64">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="w-4 h-4 text-gray-500" />
          </div>
          <Input
            type="text"
            placeholder="Search orders..."
            className="pl-10 pr-4 py-2 w-full"
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </div>
        {canManageOrders && (
          <div className="flex gap-2">
            <Button
              variant={viewType === 'my' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleViewTypeChange('my')}
            >
              My Special Orders
            </Button>
            <Button
              variant={viewType === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleViewTypeChange('all')}
            >
              All Special Orders
            </Button>
          </div>
        )}
      </div>

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>S/N</TableHead>
              <TableHead>Order No</TableHead>
              <TableHead>Date Created</TableHead>
              <TableHead>Date Needed</TableHead>
              <TableHead>Total Amount</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {specialLoading ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                    <span className="ml-2">Loading special orders...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : orderData && orderData.length > 0 ? (
              orderData.map((order: any, index: number) => (
                <TableRow key={order.id}>
                  <TableCell>
                    {' '}
                    {currentPage === 1
                      ? index + 1
                      : (currentPage - 1) * pageSize + (index + 1)}
                  </TableCell>
                  <TableCell>{order.orderNumber}</TableCell>
                  <TableCell>
                    {dayjs(order.createdAt).format('YYYY-MM-DD')}
                  </TableCell>
                  <TableCell>
                    {dayjs(order.neededBy).format('YYYY-MM-DD HH:mm A')}
                  </TableCell>
                  <TableCell>{currencyFormat(order.totalAmount)}</TableCell>
                  <TableCell>
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        order.status === 'APPROVED'
                          ? 'bg-green-100 text-green-800'
                          : order.status === 'PENDING'
                            ? 'bg-yellow-100 text-yellow-800'
                            : order.status === 'DECLINED'
                              ? 'bg-red-100 text-red-800'
                              : order.status === 'DELIVERED'
                                ? 'bg-blue-100 text-blue-800'
                                : ''
                      }`}
                    >
                      {order.status}
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleViewOrderDetails(order)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  <div className="flex flex-col items-center justify-center text-gray-500">
                    <p className="text-lg font-medium">
                      No special orders found
                    </p>
                    <p className="text-sm">
                      {debouncedSearchTerm || startDate || endDate
                        ? 'Try adjusting your search or date filters'
                        : 'Special orders will appear here once they are created'}
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {totalPages > 1 && (
        <div className="flex justify-center mt-4">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}

      <OrderDetailsModal
        open={orderDetailsOpen}
        setOpen={setOrderDetailsOpen}
        selectedOrder={selectedOrder}
        mutate={mutate}
      />
    </div>
  );
}
