'use client';

import React, { useState, useCallback } from 'react';
import { Lightbulb, Plus, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { PERMISSIONS, hasPermission } from '@/lib/types/permissions';
import {
  GetIdeas,
  GetIdeaStats,
  toggleIdeaLike,
  updateIdeaStatus,
  addIdeaComment,
  deleteIdea,
  type Idea,
  type IdeaStatus,
} from '@/api/innovation-hub/data';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { IdeaCard } from './components/idea-card';
import { CommentModal } from './components/comment-modal';
import { LoadingState } from './components/loading-state';
import { ErrorState } from './components/error-state';
import { StatsCards } from './components/stats-cards';
import { EmptyState } from './components/empty-state';
import NewIdea from './components/new-idea';
import { useUrlFilters } from '@/hooks/useUrlFilters';
import Pagination from '@/components/common/pagination';
import DateRangeFilter from '../common/date-range-filter';
import { Input } from '../ui/input';

// Configuration objects moved outside component to prevent recreation
const FILTER_OPTIONS = [
  { value: 'all', label: 'All' },
  { value: 'DRAFT', label: 'Drafts' },
  { value: 'PENDING_REVIEW', label: 'Pending Review' },
  { value: 'ACCEPTED', label: 'Accepted' },
  { value: 'REJECTED', label: 'Rejected' },
  { value: 'IMPLEMENTED', label: 'Implemented' },
];

export default function InnovationHubContent() {
  const { stats, statsMutate } = GetIdeaStats();
  const [isCommentModalOpen, setCommentModalOpen] = useState(false);
  const [selectedIdeaForComments, setSelectedIdeaForComments] =
    useState<Idea | null>(null);
  const [openNewIdeaModal, setOpenNewIdeaModal] = useState(false);

  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    handlePageChange,
    getFilter,
    getDateFilter,
    setFilter,
    setFilters,
    buildApiQuery,
  } = useUrlFilters({ initialPageSize: 10 });

  const canEdit = hasPermission(PERMISSIONS.HUB_EDIT);

  // Get current filter values from URL (no useState, no useEffect!)
  const activeTab = (getFilter('viewType') as 'all' | 'my') || 'all';
  const statusFilter = getFilter('status') || 'all';
  const startDate = getDateFilter('startDate');
  const endDate = getDateFilter('endDate');

  // Fetch ideas data
  const { ideas, isLoading, error, mutate } = GetIdeas(
    `?${buildApiQuery()}`
  );

  // Extract pagination data from API response
  const totalPages = ideas?.totalPages || 1;
  const ideasData = ideas?.ideas || [];

  // Memoized mutate handler
  const handleMutate = useCallback(() => {
    mutate();
    statsMutate();
  }, [mutate, statsMutate]);

  // Stable event handlers with useCallback
  const handleLike = useCallback(async (id: number) => {
    try {
      await toggleIdeaLike(id);
      mutate();
    } catch (error) {
      console.error('Error liking idea:', error);
    }
  }, [mutate]);

  const handleDelete = useCallback(async (id: number) => {
    try {
      await deleteIdea(id);
      mutate();
    } catch (error) {
      console.error('Error deleting idea:', error);
    }
  }, [mutate]);

  const handleStatusUpdate = useCallback(async (id: number, newStatus: IdeaStatus) => {
    try {
      await updateIdeaStatus(id, newStatus);
      mutate();
    } catch (error) {
      console.error('Error updating status:', error);
    }
  }, [mutate]);

  const handleOpenCommentModal = useCallback((idea: Idea) => {
    setSelectedIdeaForComments(idea);
    setCommentModalOpen(true);
  }, []);

  const handleCommentSubmit = useCallback(async (commentText: string) => {
    if (!selectedIdeaForComments) return;

    try {
      await addIdeaComment(selectedIdeaForComments.id, commentText);
      setCommentModalOpen(false);
      mutate();
    } catch (error) {
      console.error('Error adding comment:', error);
    }
  }, [selectedIdeaForComments, mutate]);

  const handleStatusChange = useCallback((value: string) => {
    setFilter('status', value === 'all' ? null : value);
  }, [setFilter]);

  const handleDateRangeChange = useCallback((start: Date | null, end: Date | null) => {
    setFilters({
      startDate: start ? new Date(start) : null,
      endDate: end ? new Date(end) : null,
    });
  }, [setFilters]);

  const handleTabChange = useCallback((tab: 'all' | 'my') => {
    if (tab === 'all') {
      setFilter('viewType', null);
      setFilter('myIdeas', null);
    } else {
      setFilter('viewType', 'my');
      setFilter('myIdeas', 'true');
    }
  }, [setFilter]);

  const handleClearFilters = useCallback(() => {
    setFilter('viewType', null);
    setFilter('myIdeas', null);
    setFilter('status', null);
    setFilters({
      startDate: null,
      endDate: null,
    });
    handlePageChange(1);
  }, [setFilter, setFilters, handlePageChange]);

  return (
    <>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Lightbulb className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Innovation Hub
              </h1>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Explore, share, and collaborate on cutting-edge ideas.
                <br />
                The innovation hub is a powerful initiative that can help
                transform our healthcare delivery, improve patient outcomes,
                reduce costs, and enhance staff experience.
              </p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="bg-white dark:bg-[#0F0F12] rounded-xl sm:p-4 sm:border border-gray-200 dark:border-[#1F1F23]">
          <div className="space-y-6">
            <div className="flex flex-wrap items-center justify-between space-y-2">
              <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
                <Button
                  variant={activeTab === 'all' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleTabChange('all')}
                  className="px-4 py-2"
                >
                  All Ideas
                </Button>
                <Button
                  variant={activeTab === 'my' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleTabChange('my')}
                  className="px-4 py-2"
                >
                  My Ideas
                </Button>
              </div>
              <Button
                size="sm"
                onClick={() => setOpenNewIdeaModal(true)}
                className="cursor-pointer"
              >
                <Plus className="w-3.5 h-3.5" /> New Idea
              </Button>
            </div>
            <StatsCards stats={stats} />
            <div className="flex flex-wrap gap-3 items-center ">
              <div className="relative w-full sm:w-64">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <Search className="w-4 h-4 text-gray-500" />
                </div>
                <Input
                  type="text"
                  placeholder="Search ideas..."
                  className="pl-10 pr-4 py-2 w-full"
                  value={searchTerm}
                  onChange={handleSearchChange}
                />
              </div>
              <Select value={statusFilter} onValueChange={handleStatusChange}>
                <SelectTrigger className="w-full sm:w-[220px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  {FILTER_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <DateRangeFilter
                initialStartDate={startDate}
                initialEndDate={endDate}
                onFilterChange={handleDateRangeChange}
                className="w-full sm:w-[250px]"
              />
            </div>
            {error ? (
              <ErrorState error={error} onRetry={() => mutate()} />
            ) : isLoading ? (
              <LoadingState />
            ) : ideasData.length === 0 ? (
              <EmptyState
                title={
                  debouncedSearchTerm || statusFilter !== 'all'
                    ? 'No ideas match your search'
                    : 'No ideas yet'
                }
                description={
                  debouncedSearchTerm || statusFilter !== 'all'
                    ? 'Try adjusting your search criteria or filters.'
                    : 'Be the first to share your innovative idea and inspire others!'
                }
                actionText="Clear Filters"
                onAction={
                  debouncedSearchTerm || statusFilter !== 'all'
                    ? handleClearFilters
                    : undefined
                }
              />
            ) : (
              <>
                {ideasData.map((idea: Idea) => (
                  <IdeaCard
                    key={idea.id}
                    idea={idea}
                    onLike={handleLike}
                    canEdit={canEdit}
                    onDelete={handleDelete}
                    onStatusUpdate={handleStatusUpdate}
                    onCommentClick={handleOpenCommentModal}
                  />
                ))}

                {totalPages > 1 && (
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={handlePageChange}
                  />
                )}
              </>
            )}
          </div>
        </div>

        <CommentModal
          idea={selectedIdeaForComments}
          open={isCommentModalOpen}
          onOpenChange={setCommentModalOpen}
          onCommentSubmit={handleCommentSubmit}
        />

        <NewIdea
          open={openNewIdeaModal}
          setOpen={setOpenNewIdeaModal}
          mutate={handleMutate}
        />
      </div>
    </>
  );
}
