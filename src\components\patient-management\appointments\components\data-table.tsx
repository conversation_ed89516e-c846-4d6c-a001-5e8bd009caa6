'use client';

import React, { useState } from 'react';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import { Ellipsis, Search } from 'lucide-react';
import { EmptyState, LoadingState } from '@/components/common/dataState';
import { Input } from '@/components/ui/input';
import DateRangeFilter from '@/components/common/date-range-filter';
import { GetPatientAppointments } from '@/api/crm/data';
import { PatientInteraction } from '@/components/crm/types';
import Details from './details';
import Create from './create';
import { useUrlFilters } from '@/hooks/useUrlFilters';

interface AppointmentTableProps {
  openCreate: boolean;
  setOpenCreate: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function AppointmentTable({
  openCreate,
  setOpenCreate,
}: AppointmentTableProps) {
  const [openDetails, setOpenDetails] = useState(false);
  const [selectedAppointment, setSelectedAppointment] =
    useState<PatientInteraction | null>(null);

  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    handlePageChange,
    getFilter,
    getDateFilter,
    setFilter,
    setFilters,
    buildApiQuery,
  } = useUrlFilters({ initialPageSize: 10 });

  // Get current filter values from URL (no useState, no useEffect!)
  const statusFilter = getFilter('status') || '';
  const departmentFilter = getFilter('department') || '';
  const startDate = getDateFilter('startDate');
  const endDate = getDateFilter('endDate');

  const handleEventFromModal = (appointment: PatientInteraction) => {
    setSelectedAppointment(appointment);
    setOpenDetails(true);
  };

  const { appointments, appointmentsLoading, mutate } = GetPatientAppointments(
    `?${buildApiQuery()}`
  );
  const appointmentData = appointments?.data?.appointments || [];
  const totalPages = appointments?.data?.totalPages ?? 0;

  return (
    <div className="w-full">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search appointments..."
            className="pl-8"
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </div>
        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <div className="flex gap-2">
            <button
              onClick={() => setFilter('status', 'scheduled')}
              className={`px-3 py-1 text-xs rounded-full ${
                statusFilter === 'scheduled'
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              Scheduled
            </button>
            <button
              onClick={() => setFilter('status', 'completed')}
              className={`px-3 py-1 text-xs rounded-full ${
                statusFilter === 'completed'
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              Completed
            </button>
            <button
              onClick={() => setFilter('status', 'cancelled')}
              className={`px-3 py-1 text-xs rounded-full ${
                statusFilter === 'cancelled'
                  ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              Cancelled
            </button>
            <button
              onClick={() => setFilter('status', null)}
              className={`px-3 py-1 text-xs rounded-full ${
                statusFilter === ''
                  ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              All
            </button>
          </div>
          <DateRangeFilter
            initialStartDate={startDate}
            initialEndDate={endDate}
            onFilterChange={(start, end) => {
              setFilters({
                startDate: start ? new Date(start) : null,
                endDate: end ? new Date(end) : null,
              });
            }}
            className="w-full sm:w-[250px]"
          />
        </div>
      </div>

      <div className="flex gap-2 mb-4">
        <button
          onClick={() => setFilter('department', null)}
          className={`px-3 py-1 text-xs rounded-full ${
            departmentFilter === ''
              ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400'
              : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
          }`}
        >
          All Departments
        </button>
        <button
          onClick={() => setFilter('department', 'General Medicine')}
          className={`px-3 py-1 text-xs rounded-full ${
            departmentFilter === 'General Medicine'
              ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
              : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
          }`}
        >
          General Medicine
        </button>
        <button
          onClick={() => setFilter('department', 'Cardiology')}
          className={`px-3 py-1 text-xs rounded-full ${
            departmentFilter === 'Cardiology'
              ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
              : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
          }`}
        >
          Cardiology
        </button>
        <button
          onClick={() => setFilter('department', 'Pediatrics')}
          className={`px-3 py-1 text-xs rounded-full ${
            departmentFilter === 'Pediatrics'
              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
              : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
          }`}
        >
          Pediatrics
        </button>
      </div>

      {appointmentsLoading ? (
        <LoadingState />
      ) : appointmentData.length > 0 ? (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-gray-100 dark:bg-[#1F1F23] text-xs text-gray-500 dark:text-gray-400">
                <th className="table-style">#</th>
                <th className="table-style">Date & Time</th>
                <th className="table-style">Patient</th>
                <th className="table-style">Department</th>
                <th className="table-style">Doctor</th>
                <th className="table-style">Type</th>
                <th className="table-style">Status</th>
                <th className="table-style">Actions</th>
              </tr>
            </thead>
            <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
              {appointmentData.map((appointment: any, index: number) => (
                <tr
                  className="text-xs text-[#062A55] dark:text-white"
                  key={appointment.id}
                >
                  <td className="table-style">
                    { index + 1 }
                  </td>
                  <td className="table-style">
                    {dayjs(appointment.appointmentDate).format(
                      'MMM D, YYYY h:mm A'
                    )}
                  </td>
                  <td className="table-style">{appointment.patientName}</td>
                  <td className="table-style">{appointment.department}</td>
                  <td className="table-style">{appointment.doctorName}</td>
                  <td className="table-style">
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        appointment.type === 'appointment'
                          ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                          : appointment.type === 'lab_result'
                            ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400'
                            : appointment.type === 'prescription'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                              : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
                      }`}
                    >
                      {appointment.type
                        .replace('_', ' ')
                        .charAt(0)
                        .toUpperCase() +
                        appointment.type.replace('_', ' ').slice(1)}
                    </span>
                  </td>
                  <td className="table-style">
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        appointment.status === 'completed'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                          : appointment.status === 'scheduled'
                            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                            : appointment.status === 'cancelled'
                              ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                              : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                      }`}
                    >
                      {appointment.status.charAt(0).toUpperCase() +
                        appointment.status.slice(1)}
                    </span>
                  </td>
                  <td className="table-style">
                    <Ellipsis
                      onClick={() => handleEventFromModal(appointment)}
                      className="w-4 h-4 cursor-pointer"
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          <div className="mt-4">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        </div>
      ) : (
        <EmptyState />
      )}

      {selectedAppointment && (
        <Details
          open={openDetails}
          setOpen={setOpenDetails}
          data={selectedAppointment}
          mutate={mutate}
        />
      )}

      <Create open={openCreate} setOpen={setOpenCreate} mutate={mutate} />
    </div>
  );
}
