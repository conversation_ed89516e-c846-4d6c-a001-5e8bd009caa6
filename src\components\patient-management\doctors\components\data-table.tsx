'use client';

import React, { useState } from 'react';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import { Loader2, Ellipsis, Search, UserCog } from 'lucide-react';
import { StatusBadge } from '@/components/common/status-badge';
import { EmptyState, LoadingState } from '@/components/common/dataState';
import { Input } from '@/components/ui/input';
import { GetDoctors } from '@/api/crm/data';
import Details from './details';
import Create from './create';
import { useUrlFilters } from '@/hooks/useUrlFilters';

interface DoctorTableProps {
  openCreate: boolean;
  setOpenCreate: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function DoctorTable({
  openCreate,
  setOpenCreate,
}: DoctorTableProps) {
  const [openDetails, setOpenDetails] = useState(false);
  const [selectedDoctor, setSelectedDoctor] = useState<any>(null);

  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearch<PERSON>hange,
    getFilter,
    setFilter,
  } = useUrlFilters({ initialPageSize: 10 });

  // Get current filter values from URL (no useState, no useEffect!)
  const departmentFilter = getFilter('department') || '';

  const handleEventFromModal = (doctor: any) => {
    setSelectedDoctor(doctor);
    setOpenDetails(true);
  };

  const { doctors, doctorsLoading, mutate } = GetDoctors(
    departmentFilter ? parseInt(departmentFilter) : undefined
  );

  const doctorData = doctors?.data?.doctors || [];

  // Filter doctors based on search term
  const filteredDoctors = doctorData.filter((doctor: any) => {
    if (!debouncedSearchTerm) return true;

    const searchTermLower = debouncedSearchTerm.toLowerCase();
    return (
      doctor.name.toLowerCase().includes(searchTermLower) ||
      doctor.specialization.toLowerCase().includes(searchTermLower) ||
      doctor.department.toLowerCase().includes(searchTermLower)
    );
  });

  return (
    <div className="w-full">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search doctors..."
            className="pl-8"
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </div>
        <div className="flex gap-2">
          <button
            onClick={() => setFilter('department', null)}
            className={`px-3 py-1 text-xs rounded-full ${
              departmentFilter === ''
                ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400'
                : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
            }`}
          >
            All Departments
          </button>
          <button
            onClick={() => setFilter('department', '1')}
            className={`px-3 py-1 text-xs rounded-full ${
              departmentFilter === '1'
                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
            }`}
          >
            General Medicine
          </button>
          <button
            onClick={() => setFilter('department', '2')}
            className={`px-3 py-1 text-xs rounded-full ${
              departmentFilter === '2'
                ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
            }`}
          >
            Cardiology
          </button>
          <button
            onClick={() => setFilter('department', '3')}
            className={`px-3 py-1 text-xs rounded-full ${
              departmentFilter === '3'
                ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
            }`}
          >
            Pediatrics
          </button>
        </div>
      </div>

      {doctorsLoading ? (
        <LoadingState />
      ) : filteredDoctors.length > 0 ? (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-gray-100 dark:bg-[#1F1F23] text-xs text-gray-500 dark:text-gray-400">
                <th className="table-style">#</th>
                <th className="table-style">Name</th>
                <th className="table-style">Department</th>
                <th className="table-style">Specialization</th>
                <th className="table-style">Contact</th>
                <th className="table-style">Availability</th>
                <th className="table-style">Actions</th>
              </tr>
            </thead>
            <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
              {filteredDoctors.map((doctor: any, index: number) => (
                <tr
                  className="text-xs text-[#062A55] dark:text-white"
                  key={doctor.id}
                >
                  <td className="table-style">{index + 1}</td>
                  <td className="table-style">
                    <div className="flex items-center gap-2">
                      {doctor.profileImage ? (
                        <img
                          src={doctor.profileImage}
                          alt={doctor.name}
                          className="w-8 h-8 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                          <UserCog className="w-4 h-4 text-gray-500" />
                        </div>
                      )}
                      <span>Dr. {doctor.name}</span>
                    </div>
                  </td>
                  <td className="table-style">{doctor.department}</td>
                  <td className="table-style">{doctor.specialization}</td>
                  <td className="table-style">{doctor.email}</td>
                  <td className="table-style">
                    <StatusBadge
                      status={doctor.isAvailable ? 'active' : 'inactive'}
                    />
                  </td>
                  <td className="table-style">
                    <Ellipsis
                      onClick={() => handleEventFromModal(doctor)}
                      className="w-4 h-4 cursor-pointer"
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <EmptyState message="No doctors found" />
      )}

      {selectedDoctor && (
        <Details
          open={openDetails}
          setOpen={setOpenDetails}
          data={selectedDoctor}
          mutate={mutate}
        />
      )}

      <Create open={openCreate} setOpen={setOpenCreate} mutate={mutate} />
    </div>
  );
}
