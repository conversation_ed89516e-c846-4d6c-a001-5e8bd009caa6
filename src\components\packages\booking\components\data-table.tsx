'use client';

import React, { useState } from 'react';
import { useUrlFilters } from '@/hooks/useUrlFilters';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import { Loader2, Ellipsis, Search, Package } from 'lucide-react';
import { GetPackageBookings } from '@/api/data';
import { StatusBadge } from '@/components/common/status-badge';
import Details from './details';
import { EmptyState, LoadingState } from '@/components/common/dataState';
import { Input } from '@/components/ui/input';
import DateRangeFilter from '@/components/common/date-range-filter';

// Configuration objects moved outside component to prevent recreation
const BUTTON_OPTIONS = [
  { label: 'All', param: '' },
  { label: 'Pending', param: 'status=PENDING' },
  { label: 'Completed', param: 'status=COMPLETED' },
  { label: 'Draft', param: 'status=DRAFT' },
];

const Booking = () => {
  const [open, setOpen] = useState(false);
  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    getFilter,
    getDateFilter,
    setFilter,
    setFilters,
    buildApiQuery,
  } = useUrlFilters({ initialPageSize: 10 });

  const [detail, setDetail] = useState<any | null>(null);

  // Get current filter values from URL (no useState, no useEffect!)
  const statusFilter = getFilter('status') || '';
  const startDate = getDateFilter('startDate');
  const endDate = getDateFilter('endDate');

  const { bookings, bookingLoading, mutate } = GetPackageBookings(
    `?${buildApiQuery()}`
  );
  const data = bookings?.data?.bookings;
  const totalPages = bookings?.data?.totalPages ?? 0;

  // Stable event handlers
  const handleEventFromModal = (booking: any) => {
    setDetail(booking);
    setOpen(true);
  };

  const handleSelect = (param: string) => {
    if (param) {
      const [key, value] = param.split('=');
      setFilter(key, value);
    } else {
      setFilter('status', null);
    }
  };

  return (
    <>
      <div className="w-full overflow-x-auto rounded-t-lg shadow-md">
        <div className="flex flex-wrap justify-between items-center mb-4 p-4">
          <div className="space-x-2 space-y-3">
            {BUTTON_OPTIONS.map(({ label, param }) => (
              <StatusBadge
                status={label.toLowerCase()}
                className="cursor-pointer"
                key={label}
                onClick={() => handleSelect(param)}
              />
            ))}
          </div>
          <div className="flex flex-wrap gap-3 items-center">
            <DateRangeFilter
              initialStartDate={startDate}
              initialEndDate={endDate}
              onFilterChange={(start, end) => {
                setFilters({
                  startDate: start ? new Date(start) : null,
                  endDate: end ? new Date(end) : null,
                });
              }}
              className="w-[250px]"
            />
            <div className="relative w-64">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Search className="w-4 h-4 text-gray-500" />
              </div>
              <Input
                type="text"
                placeholder="Search bookings..."
                className="pl-10 pr-4 py-2 w-full"
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </div>
          </div>
        </div>
        <table className="w-full table-auto text-left text-xs">
          <thead className="bg-primary text-gray-100 dark:text-black">
            <tr>
              <th className="table-style">S/N</th>
              <th className="table-style">Date</th>
              <th className="table-style">Email</th>
              <th className="table-style">Location</th>
              <th className="table-style">Reference</th>
              <th className="table-style">Package</th>
              <th className="table-style">Status</th>
              <th className="table-style">More</th>
            </tr>
          </thead>
          <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
            {bookingLoading ? (
              <tr>
                <td colSpan={8} className="text-center py-8">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                    <span className="ml-2">Loading bookings...</span>
                  </div>
                </td>
              </tr>
            ) : data && data.length > 0 ? (
              data.map((booking: any, index: number) => (
                <tr
                  className="text-xs text-[#062A55] dark:text-white"
                  key={booking.id}
                >
                  <td className="table-style">
                    {currentPage === 1
                      ? index + 1
                      : (currentPage - 1) * pageSize + (index + 1)}
                  </td>
                  <td className="table-style">
                    {dayjs(booking.createdAt).format('MMMM D, YYYY')}
                  </td>
                  <td className="table-style">{booking.user.emailAddress}</td>
                  <td className="table-style">{booking.location}</td>
                  <td className="table-style">{booking.bookingRef}</td>
                  <td className="table-style">{booking.packages.name}</td>
                  <td className="table-style">
                    {StatusBadge({
                      status: booking.bookingStatus.toLowerCase(),
                    })}
                  </td>
                  <td className="table-style">
                    <Ellipsis
                      onClick={() => handleEventFromModal(booking)}
                      className="w-4 h-4 cursor-pointer"
                    />
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={8} className="text-center py-8">
                  <div className="flex flex-col items-center justify-center text-gray-500">
                    <Package className="h-12 w-12 mb-2 opacity-50" />
                    <p className="text-lg font-medium">No bookings found</p>
                    <p className="text-sm">
                      {debouncedSearchTerm || startDate || endDate
                        ? 'Try adjusting your search or date filters'
                        : 'Bookings will appear here once they are created'}
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      {totalPages > 1 ? (
        <Pagination
          totalPages={totalPages}
          currentPage={currentPage}
          onPageChange={handlePageChange}
        />
      ) : (
        ''
      )}
      {open && (
        <Details open={open} setOpen={setOpen} mutate={mutate} data={detail} />
      )}
    </>
  );
};

export default Booking;
