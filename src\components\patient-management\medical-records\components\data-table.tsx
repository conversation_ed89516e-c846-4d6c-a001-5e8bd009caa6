'use client';

import React, { useState } from 'react';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import {
  Loader2,
  Ellipsis,
  Search,
  ClipboardList,
  FileText,
} from 'lucide-react';
import { StatusBadge } from '@/components/common/status-badge';
import { EmptyState, LoadingState } from '@/components/common/dataState';
import { Input } from '@/components/ui/input';
import DateRangeFilter from '@/components/common/date-range-filter';
import { GetAllMedicalRecords } from '@/api/crm/data';
import { useUrlFilters } from '@/hooks/useUrlFilters';
import Details from './details';
import Create from './create';

interface MedicalRecordsTableProps {
  openCreate: boolean;
  setOpenCreate: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function MedicalRecordsTable({
  openCreate,
  setOpenCreate,
}: MedicalRecordsTableProps) {
  const [openDetails, setOpenDetails] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);

  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    handlePageChange,
    getFilter,
    getDateFilter,
    setFilter,
    setFilters,
    buildApiQuery,
  } = useUrlFilters({ initialPageSize: 10 });

  // Get current filter values from URL (no useState, no useEffect!)
  const recordTypeFilter = getFilter('recordType') || '';
  const startDate = getDateFilter('startDate');
  const endDate = getDateFilter('endDate');

  const handleEventFromModal = (record: any) => {
    setSelectedRecord(record);
    setOpenDetails(true);
  };

  const { medicalRecords, medicalRecordsLoading, mutate } =
    GetAllMedicalRecords(`?${buildApiQuery()}`);

  const medicalRecordData = medicalRecords?.data?.medicalRecords || [];
  const totalPages = medicalRecords?.data?.totalPages ?? 0;

  return (
    <div className="w-full">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search medical records..."
            className="pl-8"
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </div>
        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <div className="flex gap-2 flex-wrap">
            <button
              onClick={() => setFilter('recordType', null)}
              className={`px-3 py-1 text-xs rounded-full ${
                recordTypeFilter === ''
                  ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              All Types
            </button>
            <button
              onClick={() => setFilter('recordType', 'consultation')}
              className={`px-3 py-1 text-xs rounded-full ${
                recordTypeFilter === 'consultation'
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              Consultation
            </button>
            <button
              onClick={() => setFilter('recordType', 'lab')}
              className={`px-3 py-1 text-xs rounded-full ${
                recordTypeFilter === 'lab'
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              Lab Results
            </button>
            <button
              onClick={() => setFilter('recordType', 'procedure')}
              className={`px-3 py-1 text-xs rounded-full ${
                recordTypeFilter === 'procedure'
                  ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              Procedure
            </button>
          </div>
          <DateRangeFilter
            initialStartDate={startDate}
            initialEndDate={endDate}
            onFilterChange={(start, end) => {
              setFilters({
                startDate: start ? new Date(start) : null,
                endDate: end ? new Date(end) : null,
              });
            }}
            className="w-full sm:w-[250px]"
          />
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-100 dark:bg-[#1F1F23] text-xs text-gray-500 dark:text-gray-400">
              <th className="table-style">#</th>
              <th className="table-style">Date</th>
              <th className="table-style">Patient</th>
              <th className="table-style">Record Type</th>
              <th className="table-style">Doctor</th>
              <th className="table-style">Diagnosis</th>
              <th className="table-style">Actions</th>
            </tr>
          </thead>
          <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
            {medicalRecordsLoading ? (
              <tr>
                <td colSpan={7} className="text-center py-8">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                    <span className="ml-2">Loading medical records...</span>
                  </div>
                </td>
              </tr>
            ) : medicalRecordData && medicalRecordData.length > 0 ? (
              medicalRecordData.map((record: any, index: number) => (
                <tr
                  className="text-xs text-[#062A55] dark:text-white"
                  key={record.id}
                >
                  <td className="table-style">
                    { index + 1
                   }
                  </td>
                  <td className="table-style">
                    {dayjs(record.recordDate).format('MMM D, YYYY')}
                  </td>
                  <td className="table-style">{record.patientName}</td>
                  <td className="table-style">
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        record.recordType === 'consultation'
                          ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                          : record.recordType === 'lab'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                            : record.recordType === 'procedure'
                              ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400'
                              : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
                      }`}
                    >
                      {record.recordType.charAt(0).toUpperCase() +
                        record.recordType.slice(1)}
                    </span>
                  </td>
                  <td className="table-style">{record.doctorName}</td>
                  <td className="table-style max-w-[200px] truncate">
                    {record.diagnosis}
                  </td>
                  <td className="table-style">
                    <Ellipsis
                      onClick={() => handleEventFromModal(record)}
                      className="w-4 h-4 cursor-pointer"
                    />
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="text-center py-8">
                  <div className="flex flex-col items-center justify-center text-gray-500">
                    <FileText className="h-12 w-12 mb-2 opacity-50" />
                    <p className="text-lg font-medium">
                      No medical records found
                    </p>
                    <p className="text-sm">
                      {debouncedSearchTerm || startDate || endDate
                        ? 'Try adjusting your search or date filters'
                        : 'Medical records will appear here once they are created'}
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
        <div className="mt-4">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      </div>

      {selectedRecord && (
        <Details
          open={openDetails}
          setOpen={setOpenDetails}
          data={selectedRecord}
          mutate={mutate}
        />
      )}

      <Create open={openCreate} setOpen={setOpenCreate} mutate={mutate} />
    </div>
  );
}
