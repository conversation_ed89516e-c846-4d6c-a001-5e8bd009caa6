'use client';

import { useState, useMemo, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { GetAllOrders, GetGeneralOrders } from '@/api/cafeteria/menu';
import { currencyFormat } from '@/lib/utils';
import DateRangeFilter from '@/components/common/date-range-filter';
import MonthYearFilter from '@/components/common/month-year-filter';
import dayjs from 'dayjs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ShoppingCart, Search, Eye, Printer } from 'lucide-react';
import { useUrlFilters } from '@/hooks/useUrlFilters';
import Pagination from '@/components/common/pagination';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import OrderStats from './components/OrderStats';
import OrderSpecial from './components/OrderSpecial';
import OrderDetailsModal from './components/OrderDetailsModal';
import { myApi } from '@/api/fetcher';


// Configuration objects moved outside component to prevent recreation
const PAYMENT_TYPE_OPTIONS = [
  { value: 'all', label: 'All Payment Types' },
  { value: 'cash', label: 'Cash' },
  { value: 'card', label: 'Card' },
  { value: 'transfer', label: 'Transfer' },
];

const SALES_TYPE_OPTIONS = [
  { value: 'all', label: 'All Sales Types' },
  { value: 'dine-in', label: 'Dine In' },
  { value: 'takeaway', label: 'Takeaway' },
  { value: 'delivery', label: 'Delivery' },
];

export default function OrdersManagement() {
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [orderDetailsOpen, setOrderDetailsOpen] = useState(false);
  const [showOrderSpecial, setShowOrderSpecial] = useState(false);
  const [showMenuItemSales, setShowMenuItemSales] = useState(false);

  const canManageOrders = hasPermission(PERMISSIONS.CAFETERIA_ORDERS_MANAGE);
  const canAccessPOS = hasPermission(PERMISSIONS.CAFETERIA_POS_ACCESS);

  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    getFilter,
    getDateFilter,
    setFilter,
    setDateFilter,
    setFilters,
    buildApiQuery,
  } = useUrlFilters({ initialPageSize: 15 });

  // Get current filter values from URL (no useState, no useEffect!)
  const selectedMonth = getFilter('month') || '';
  const selectedYear = getFilter('year') || '';
  const selectedPresetDate = getFilter('presetDate') || '';
  const selectedPayment = getFilter('paymentType') || '';
  const selectedSalesType = getFilter('saleType') || '';
  const startDate = getDateFilter('startDate');
  const endDate = getDateFilter('endDate');
  const viewType = (getFilter('viewType') as 'my' | 'all') || (canManageOrders ? 'all' : 'my');



  // Build API query once
  const apiQuery = buildApiQuery();

  // Always call both hooks but use conditional logic for data usage
  const {
    orders: myOrders,
    orderLoading: myOrderLoading,
    mutate: mutateMyOrders,
  } = GetAllOrders(`?${apiQuery}`);

  const {
    orders: generalOrders,
    orderLoading: generalOrderLoading,
    mutate: mutateGeneralOrders,
  } = canManageOrders
    ? GetGeneralOrders(`?${apiQuery}`)
    : { orders: null, orderLoading: false, mutate: () => {} };

  // Use appropriate orders data based on viewType
  const orders = viewType === 'all' ? generalOrders : myOrders;
  const orderLoading = viewType === 'all' ? generalOrderLoading : myOrderLoading;
  const orderData = orders?.data?.orders || [];
  const aggregatedItems = orders?.data?.aggregatedItems || [];
  const totalPages = orders?.data?.totalPages ?? 0;

  // Memoize stats to prevent unnecessary re-renders
  const stats = useMemo(() => ({
    overallOrders: orders?.data?.overallOrders || 0,
    totalOrders: orders?.data?.totalOrders || 0,
    totalAmount: orders?.data?.totalAmount || 0,
  }), [orders?.data]);

  const orderStatsLoading = orderLoading;

  // Stable event handlers with useCallback
  const handleMonthYearChange = useCallback((month: string, year: string) => {
    setFilter('month', month || null);
    setFilter('year', year || null);
  }, [setFilter]);

  const handlePaymentTypeChange = useCallback((value: string) => {
    setFilter('paymentType', value && value !== 'all' ? value : null);
  }, [setFilter]);

  const handleSalesTypeChange = useCallback((value: string) => {
    setFilter('saleType', value && value !== 'all' ? value : null);
  }, [setFilter]);

  const handleViewTypeChange = useCallback((value: 'my' | 'all') => {
    setFilter('viewType', value);
  }, [setFilter]);

  const handleViewOrderDetails = useCallback((order: any) => {
    setSelectedOrder(order);
    setOrderDetailsOpen(true);
  }, []);

  const handleDateRangeChange = useCallback((start: Date | null, end: Date | null) => {
    setFilters({
      startDate: start ? new Date(start) : null,
      endDate: end ? new Date(end) : null,
    });
  }, [setFilters]);

  const handleOrderSpecialMutate = useCallback(() => {
    if (viewType === 'my') {
      mutateMyOrders();
    } else {
      mutateGeneralOrders();
    }
  }, [viewType, mutateMyOrders, mutateGeneralOrders]);

  const handleOrderSpecialBack = useCallback(() => {
    setShowOrderSpecial(false);
  }, []);

  // Print receipt handler with useCallback
  const handlePrintReceipt = useCallback(async (orderId: string) => {
    try {
      const response = await myApi.get(
        `/cafeteria/orders/print-receipt/${orderId}`
      );
      const receiptHtml = response.data.data.receiptHtml;

      const iframe = document.createElement('iframe');
      iframe.style.position = 'absolute';
      iframe.style.left = '-9999px';
      document.body.appendChild(iframe);

      iframe.contentDocument?.open();
      iframe.contentDocument?.write(receiptHtml);
      iframe.contentDocument?.close();

      iframe.contentWindow?.focus();
      iframe.contentWindow?.print();

      setTimeout(() => document.body.removeChild(iframe), 1000);
    } catch (printError) {
      console.log('Receipt printing failed', printError);
    }
  }, []);

  if (showOrderSpecial) {
    return (
      <OrderSpecial
        onBack={handleOrderSpecialBack}
        mutate={handleOrderSpecialMutate}
      />
    );
  }

  return (
    <div className="space-y-4">
      <Button onClick={() => setShowOrderSpecial(true)} size="sm">
        Order Special
      </Button>
      <div className="flex flex-wrap gap-3 items-center">
        <MonthYearFilter
          onFilterChange={handleMonthYearChange}
          className="flex gap-2"
        />
        <Select
          value={selectedPresetDate}
          onValueChange={(value) => setFilter('presetDate', value === 'all' ? null : value)}
        >
          <SelectTrigger className="w-[160px]">
            <SelectValue placeholder="Preset Date Filters" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">No preset</SelectItem>
            <SelectItem value="today">Today</SelectItem>
            <SelectItem value="yesterday">Yesterday</SelectItem>
            <SelectItem value="last7days">Last 7 Days</SelectItem>
            <SelectItem value="thisMonth">This Month</SelectItem>
            <SelectItem value="lastMonth">Last Month</SelectItem>
          </SelectContent>
        </Select>
        <DateRangeFilter
          initialStartDate={startDate}
          initialEndDate={endDate}
          onFilterChange={handleDateRangeChange}
          className="w-[250px]"
        />
        <Select value={selectedPayment} onValueChange={handlePaymentTypeChange}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="Payment Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Payments</SelectItem>
            <SelectItem value="credit">Credit</SelectItem>
            <SelectItem value="card">Card</SelectItem>
            <SelectItem value="transfer">Transfer</SelectItem>
            <SelectItem value="wallet">Wallet</SelectItem>
            <SelectItem value="voucher">Meal Voucher</SelectItem>
          </SelectContent>
        </Select>

        {viewType === 'all' && (
          <Select
            value={selectedSalesType}
            onValueChange={handleSalesTypeChange}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Sales Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="staff">Staff</SelectItem>
              <SelectItem value="general">General</SelectItem>
            </SelectContent>
          </Select>
        )}
        <div className="relative w-64">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="w-4 h-4 text-gray-500" />
          </div>
          <Input
            type="text"
            placeholder="Search ..."
            className="pl-10 pr-4 py-2 w-full"
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </div>
        {canManageOrders && (
          <div className="flex gap-2">
            <Button
              variant={viewType === 'my' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleViewTypeChange('my')}
            >
              My Orders
            </Button>
            <Button
              variant={viewType === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleViewTypeChange('all')}
            >
              All Orders
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => setShowMenuItemSales(true)}
            >
              View Menu Item Sale
            </Button>
          </div>
        )}
      </div>
      <OrderStats
        stats={stats}
        orderStatsLoading={orderStatsLoading}
        viewType={viewType}
        startDate={startDate || undefined}
        endDate={endDate || undefined}
      />

      {showMenuItemSales && (
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowMenuItemSales(false)}
        >
          Back to Order List
        </Button>
      )}

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              {showMenuItemSales ? (
                <>
                  <TableHead>S/N</TableHead>
                  <TableHead>Menu Item</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Total Amount</TableHead>
                </>
              ) : (
                <>
                  <TableHead>S/N</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Order No</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Sales Type</TableHead>
                  <TableHead>Payment</TableHead>
                  <TableHead>Total Amount</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {orderLoading ? (
              <TableRow>
                <TableCell
                  colSpan={showMenuItemSales ? 4 : 8}
                  className="text-center py-8"
                >
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                    <span className="ml-2">Loading...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : showMenuItemSales ? (
              aggregatedItems && aggregatedItems.length > 0 ? (
                aggregatedItems.map((item: any, index: number) => (
                  <TableRow key={item.id || index}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell>{item.menuName || 'N/A'}</TableCell>
                    <TableCell>{item.totalQuantity || 0}</TableCell>
                    <TableCell>
                      {currencyFormat(item.totalPrice || 0)}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-8">
                    <div className="flex flex-col items-center justify-center text-gray-500">
                      <ShoppingCart className="h-12 w-12 mb-2 opacity-50" />
                      <p className="text-lg font-medium">No menu items found</p>
                    </div>
                  </TableCell>
                </TableRow>
              )
            ) : orderData && orderData.length > 0 ? (
              orderData.map((order: any, index: number) => (
                <TableRow key={order.id || index}>
                  <TableCell>
                    {currentPage === 1
                      ? index + 1
                      : (currentPage - 1) * pageSize + (index + 1)}
                  </TableCell>
                  <TableCell>
                    {dayjs(order.createdAt).format('YYYY-MM-DD')}
                  </TableCell>
                  <TableCell>{order.orderNumber || 'N/A'}</TableCell>
                  <TableCell>{order.orderType || 'N/A'}</TableCell>
                  <TableCell>
                    {order?.saleType?.toUpperCase() || 'N/A'}
                  </TableCell>
                  <TableCell>{order.paymentType || 'N/A'}</TableCell>
                  <TableCell>
                    {currencyFormat(order.totalAmount || 0)}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end">
                      <Button className='cursor-pointer'
                        variant="ghost"
                        size="icon"
                        onClick={() => handleViewOrderDetails(order)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                         <Button className='cursor-pointer'
                        variant="ghost"
                        size="icon"
                        onClick={() => handlePrintReceipt(order.id)}
                      >
                        <Printer className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  <div className="flex flex-col items-center justify-center text-gray-500">
                    <ShoppingCart className="h-12 w-12 mb-2 opacity-50" />
                    <p className="text-lg font-medium">No orders found</p>
                    <p className="text-sm">
                      {debouncedSearchTerm || startDate || endDate
                        ? 'Try adjusting your search or date filters'
                        : 'Orders will appear here once they are created'}
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {!showMenuItemSales && totalPages > 1 && (
        <div className="flex justify-center mt-4">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}

      <OrderDetailsModal
        open={orderDetailsOpen}
        setOpen={setOrderDetailsOpen}
        selectedOrder={selectedOrder}
      />
    </div>
  );
}
